import AuthenticationService from './authenticationService';
import SyncEngine from './syncEngine';
import DatabaseManager, { DatabaseType } from './database/databaseManager';
import FrameworkHelper from './helper/frameworkHelper';
import { FrameworkSettingsFields, FrameworkSettingsManager } from './helper/frameworkSettingsManager';
import { UserSettingsManager, UserSettingsFields } from './helper/userSettingsManager';
import OutboxService from './outbox/outboxService';
import { NotificationListenerHelper } from './helper/notificationListnerHelper';
import AttachmentHelper from './attachment/attachmentHelper';
import AttachmentQHelper from './attachment/attachmentQHelper';
import * as ServiceConstants from "./helper/serviceConstants";
import * as FieldConstants from './applicationMeta/fieldConstants';
import InboxHelper from './inbox/inboxHelper';
import OutBoxHelper from './outbox/outboxHelper';
import HttpConnection from './helper/httpConnection';
import SettingsHelper from './helper/settingsHelper';
import AppDatabaseManager from './database/appDatabaseManager';
import { UnviredAccountManager } from './helper/unviredAccountManager';
import { UnviredAccount } from './helper/unviredAccount';
import { isServerReachable } from "./helper/utils";

var parameters;

var UMP = function () { };

var helper = function () { };

const listenerType = {
  auth_activation_required: 0,
  app_requires_login: 1,
  auth_activation_success: 2,
  auth_activation_error: 3,
  login_success: 4,
  login_error: 5
};

/**
 *  loginMode is used to check for specific parameters for different mode
 */
const loginMode = {
  authActivate: 0,
  authLocal: 1,
  forgotPassword: 2,
};

const loginType = {
  unvired: "UNVIRED_ID",
  ads: "ADS",
  sap: "SAP",
  custom: "CUSTOM",
  email: "EMAIL",
  saml2: "SAML2"
};

/**
 * result type in returned callbackResult
 */
const resultType = {
  success: 0,
  error: 1,
};

const requestType = {
  RQST: "RQST",
  PULL: "PULL",
  PUSH: "PUSH",
  QUERY: "QUERY",
  REQ: "REQ"
};

UMP.prototype.loginMode = loginMode;
UMP.prototype.loginType = loginType;
UMP.prototype.resultType = resultType;
UMP.prototype.requestType = requestType;
UMP.prototype.listenerType = listenerType;

UMP.prototype.isMobile = function () {
  if (window.cordova.platformId == 'browser') { // TODO: add condition for electron
    return false;
  }
  return true;
};


/**
 * Mobile platform only
 * Returns the location of the log file.
 */
UMP.prototype.getLogFilePath = function (success, fail) {
  try {
    const logFilePath = Logger.getLogFileURL()
    success(logFilePath)
  } catch (error) {
    fail(error)
  }
};

/**
 * Mobile platform only
 * Returns the location of the log file.
 */
UMP.prototype.testPushNotification = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("test")
  } catch (error) {
    fail(error)
  }
};

/**
 * Returns the current log level.
 */
UMP.prototype.getLogLevel = async function (success, fail) {
  try {
    const logLevel = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.logLevel);
    success(logLevel)
  } catch (error) {
    fail(error)
  }
};

/**
 * Set the log level of the app.
 * @param logLevel The log level to set
 */
UMP.prototype.setLogLevel = function (logLevel, success, fail) {
  try {
    SettingsHelper.setLogLevel(logLevel + "");
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.logDebug = function (sourceClass, method, message) {
  Logger.logDebug(sourceClass, method, message);
};

UMP.prototype.logError = function (sourceClass, method, message) {
  Logger.logError(sourceClass, method, message);
};

UMP.prototype.logInfo = function (sourceClass, method, message) {
  Logger.logInfo(sourceClass, method, message);
};

UMP.prototype.logRead = function (success, fail) {
  try {
    const logContent = Logger.getLogFileContent()
    success(logContent)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.logDelete = function () {
  try {
    Logger.clearLogFile()
    success("")
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.sendLogToServer = async function (success, fail) {
  try {
    await SettingsHelper.sendLogsToServer()
    await SettingsHelper.sendAppDbToServer();
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.sendLogViaEmail = async function (success, fail) {
  try {
    const logZipPath = await SettingsHelper.createAndGetLogZipPath()
    success(logZipPath)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.login = async function (loginParameters, success, fail) {
  parameters = loginParameters;
  try {
    console.log(document.body)
    const result = await AuthenticationService.login(loginParameters);
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};

UMP.prototype.loginWithDemoData = async function (loginParameters, success, fail) {
  parameters = loginParameters;
  try {
    const result = await AuthenticationService.loginWithDemoData(loginParameters);
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};

/**;
 * logout() - Close all database and shut down all thread
 */
UMP.prototype.logout = async function (success, fail) {
  try {
    const result = await AuthenticationService.logout();
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};

/**
 * authenticateAndActivate - authenticate and activate application against ump. Framework gives callback to registered LoginListener
 * Returns response object with Type (success/failure) and message
 *
 * Example - ump.login.parameters.appName = "SAP_ERP_SO_TEMPLATE";
 *           ump.login.parameters.username ="TARAK";
 *           ump.login.parameters.url = "http://demo.unvired.io/UMP";
 *           ump.login.parameters.company = "UNVIRED";
 *
 *           ump.login.authenticateAndActivate(callback(res){ });
 *
 *  in Login listener callback check   if(res.type === ump.login.listenerType.auth_success){  //Navigate to Application Home  }
 *
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 */
UMP.prototype.authenticateAndActivate = async function (loginParameters, success, fail) {
  try {
    const result = await AuthenticationService.authenticateAndActivate(loginParameters);
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};
/**
 * authenticateLocal - Authenticate with username,password saved in database
 *
 * Example - ump.login.parameters.username ="TARAK";
 *           ump.login.parameters.password = "MS123*";
 *
 *           ump.login.authenticateLocal(callback(res){ });
 *
 *  in Login listener callback check   if(res.type === ump.login.listenerType.login_success){  //Handle login success  }
 *
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 *
 *  Mobile Only api
 */
UMP.prototype.authenticateLocal = async function (loginParameters, success, fail) {
  try {
    const result = await AuthenticationService.authenticateLocal(loginParameters);
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};
/**
 * getAllAccount - Get all existing Account
 *
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 *
 *  Mobile Only api
 */
UMP.prototype.getAllAccounts = async function (success, fail) {
  try {
    const result = await AuthenticationService.getAllAccounts();
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};
/**
 * switchAccount - Switch to given Account.
 *
 *  @param {object} account - Account to switch
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 *
 *  Mobile Only api
 */
UMP.prototype.switchAccount = async function (account, success, fail) {
  try {
    const result = await AuthenticationService.switchAccount(account);
  // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};
/**
 * deleteAccount - Delete given Account
 *
 *  @param {object} account - Account to switch
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 *
 * Mobile Only api
 */
UMP.prototype.deleteAccount = async function (account, success, fail) {
  try {
    const result = await AuthenticationService.deleteAccount(account);
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};

/*
  * setClientCredentials - Set array of client credentials
  */
UMP.prototype.setClientCredentials = async function (credentials, success, fail) {
  try {
    const result = await AuthenticationService.setClientCredentials(credentials);
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};

/*
  * isClientCredentialsSet - Returns true if client credentials already set else false
  */
UMP.prototype.isClientCredentialsSet = async function (success, fail) {
  try {
  const result = await AuthenticationService.isClientCredentialsSet();
    // Handle successful login (optional, result might not be used)
    success(result)
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};

/**
 * getInfoMessages - Get list of InfoMessages
 */
UMP.prototype.getInfoMessages = async function (headerName, lid, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success([])
  } catch (error) {
    // Handle login failure
    fail(error)
  }
};
/**
 * userSettings - Get current User information
 * @param {function} callback
 */
UMP.prototype.userSettings = async function (success, fail) {
  try {
    const unviredAccount = UnviredAccountManager.getInstance().getLastLoggedInAccount();
    if (!unviredAccount) {
      fail("No account found")
      return;
    }
    var jwtToken = HttpConnection.jwtAuthToken;
    if (jwtToken == null || jwtToken == undefined || jwtToken === "") {
      jwtToken = unviredAccount.getJwtToken();
      HttpConnection.jwtAuthToken = jwtToken;
    }
    const serverType = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.serverType);
    const unviredUserId = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.unviredUserId);
    const unviredPassword = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.unviredPassword);
    const userSettings = {
      "UNVIRED_ID": unviredUserId,
      "USER_ID": AuthenticationService.loginParameters.username || unviredUserId,
      "FIRST_NAME": unviredAccount.getFirstName(),
      "LAST_NAME": unviredAccount.getLastName(),
      "FULL_NAME": "",
      "EMAIL": "",
      "SERVER_URL": unviredAccount.getServerURL(),  
      "SERVER_TYPE": serverType || "",
      "SAP_USER_ID": "",                        
      "SAP_PORT_NAME": "",                            
      "LOGIN_TYPE": AuthenticationService.loginParameters.loginType,                               
      "ADS_USER_ID": "",
      "ADS_DOMAIN": "",
      "UNVIRED_PASSWORD": unviredPassword ?? "", 
      "JWT_TOKEN": jwtToken
    }
    success({data: userSettings})
  } catch (error) {
    fail(error)
  }
};
/**
 * updateSystemCredentials - Save System Credentials
 */
UMP.prototype.updateSystemCredentials = function (credentials, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};
/**
 * getSystemCredentials - Get all System Credentials
 */
UMP.prototype.getSystemCredentials = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success([])
  } catch (error) {
    fail(error)
  }
};
/**
 *  Get Version Infrmation
 */
UMP.prototype.getVersionNumbers = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};
/**
 * clearData - clear application databases and files
 */
UMP.prototype.clearData = async function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    const result = await AuthenticationService.clearData();
    success(result)
  } catch (error) {
    fail(error)
  }
};
/**
 * Check for Internet connection
 */
UMP.prototype.hasInternet = async function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    if (!(await isServerReachable(AuthenticationService.loginParameters.url))) {
      success(false)
    } else {
      success(true)
    }
  } catch (error) {
    fail(error)
  }
};

/**
 * pullDb - pull database file to "temp" folder for development purpose only
 *
 * @param {function} callback - (Optional) user supplied async callback / error handler
 */
UMP.prototype.pullDb = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};
/**
 * pushDB - push updated database file from "temp" folder to application directory for development purpose only
 *
 * @param {function} callback - (Optional) user supplied async callback / error handler
 */
UMP.prototype.pushDb = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};
/**
 * encrypt - Get encrypted string
 */
UMP.prototype.encrypt = function (input, success, fail) {
  try {
  // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};
/**
 * decrypt - Get decrypted string
 */
UMP.prototype.decrypt = function (input, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};

/**
 * encrypt - Get encrypted string
 * @param {function} callback
 */
UMP.prototype.decrypt = function (input, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};

/**
 * Guid
 */
UMP.prototype.guid = function () {
  return helper.guid();
};

/**
 * select - select records from table
 *
 * Example - ump.db.select("CUSTOMERS_RESULTS_HEADER",{'F_NAME':'TARAK','EMP_NO':'0039'},function(result){});
 *
 * @param {string} tableName table name
 * @param {object} whereClause Json object contains field name-value
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 * e.g.
 */
//TODO Handle != clause
UMP.prototype.dbSelect = async function (tableName, whereClause, success, fail) {
  if (typeof whereClause == 'object') {
    whereClause = buildWhereClause(whereClause)
  }
  try {
    const result = await AppDatabaseManager.getInstance().select(tableName, whereClause);  
    success({data: result, type: resultType.success})
  } catch (error) {
    fail({error: error, type: resultType.error})
  }
};
/**
 * insert - insert record into table
 * In borwser insert always insert or update based on gid
 * Example - ump.db.insert("CUSTOMER_HEADER",{"NAME":"TARAK","NO":"0039"....},true/false,callback);
 *
 * @param {string} tableName table name
 * @param {Object} structureObject - Json object contains field name-value
 * @param {boolean} isHeader - is dataStructure a header or item?
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.dbInsert = async function (tableName, structureObject, isHeader, success, fail) {
  try {
    const result = await AppDatabaseManager.getInstance().insert(tableName, structureObject, isHeader);
    success({message: result, type: resultType.success})
  } catch (error) {
    fail({error: error, type: resultType.error})
  }
};
/**
 * insertOrUpdate - insert record or update record if exists into table
 *
 * Example - ump.db.insert("CUSTOMER_HEADER",{"NAME":"TARAK","NO":"0039"....},true/false,callback);
 *
 * @param {string} tableName table name
 * @param {Object} structureObject - Json object contains field name-value
 * @param {boolean} isHeader - is dataStructure a header or item?
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.dbInsertOrUpdate = async function (tableName, structureObject, isHeader, success, fail) {
  try {
    const result = await AppDatabaseManager.getInstance().insertOrUpdate(tableName, structureObject, isHeader);
    success({message: result, type: resultType.success})
  } catch (error) {
    fail({error: error, type: resultType.error})
  }
};
/**
 * deleteRecord - delete record entry from table
 *
 * Example - ump.db.deleteRecord("CUSTOMER_HEADER",{'EMP_NO':'0039'},callback);
 *
 * @param {string} tableName - table name
 * @param {object} whereClause - (Optional)Json object contains field name-value
 * @param {function} callback - (Optional) user supplied async callback / error handler
 */
UMP.prototype.dbDelete = async function (tableName, whereClause, success, fail) {
  if (typeof whereClause == 'object') {
    whereClause = buildWhereClause(whereClause)
  }
  try {
    const result = await AppDatabaseManager.getInstance().delete(tableName, whereClause);
    success({message: result, type: resultType.success})
  } catch (error) {
    fail({error: error, type: resultType.error})
  }
};
/**
 * update - update existing record entry in table
 *
 * Example - ump.db.update("CUSTOMER_HEADER",{'SSN':'0097658'},{'EMP_NO':'0039'},callback);
 *
 * @param {string} tableName - table name
 * @param {object} updatedObject - Json object contains only updated field name-value
 * @param {object} whereClause - Json object contains field name-value
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.dbUpdate = async function (tableName, updatedObject, whereClause, success, fail) {
  if (typeof whereClause == 'object') {
    whereClause = buildWhereClause(whereClause)
  }
  try {
    const result = await AppDatabaseManager.getInstance().update(tableName, updatedObject, whereClause);
    success({message: result, type: resultType.success})
  } catch (error) {
    fail({error: error, type: resultType.error})
  }
};
/**
 * executeStatement - execure raw query
 *
 * Example - ump.db.executeStatement("SELECT name,COUNT(*) as COUNT FROM CUSTOMERS_RESULTS_HEADER",function(result){// check result.data});
 *
 * @parem {string} query - complete sql query to be executed
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 * Mobile Only api
 */
UMP.prototype.dbExecuteStatement = async function (query, success, fail) {
  try {
    const result = await AppDatabaseManager.getInstance().executeStatement(query);
    Logger.logDebug("UMP", "dbExecuteStatement", "result: " + JSON.stringify(result));
    success({data: result, type: resultType.success})
  } catch (error) {
    Logger.logDebug("UMP", "dbExecuteStatement", "error: " + error);
    fail({error: error, type: resultType.error})
  }
};
/**
 * createSavePoint - create a save point for db transaction
 */
UMP.prototype.dbCreateSavePoint = async function (savePoint) {
  try {
    await AppDatabaseManager.getInstance().createSavePoint(savePoint);
  } catch (error) {
    Logger.logError("UMP", "dbCreateSavePoint", error);
  }
};
/**
 * releaseSavePoint - release a save point for db transaction
 */
UMP.prototype.dbReleaseSavePoint = async function (savePoint) {
  try {
    await AppDatabaseManager.getInstance().releaseSavePoint(savePoint);
  } catch (error) {
    Logger.logError("UMP", "dbReleaseSavePoint", error);
  }
};
/**
 * rollbackSavePoint - rollback a save point for db transaction
 */
UMP.prototype.dbRollbackToSavePoint = async function (savePoint) {
  try {
    await AppDatabaseManager.getInstance().rollbackToSavePoint(savePoint);
  } catch (error) {
    Logger.logError("UMP", "dbRollbackToSavePoint", error);
  }
};
/**
 * beginTransaction - Begin a db transaction
 */
UMP.prototype.dbBeginTransaction = async function () {
  try {
    await AppDatabaseManager.getInstance().beginTransaction();
  } catch (error) {
    Logger.logError("UMP", "dbBeginTransaction", error);
  }
};
/**
 * endTransaction - End a db transaction
 */
UMP.prototype.dbEndTransaction = async function () {
  try {
    await AppDatabaseManager.getInstance().endTransaction();
  } catch (error) {
    Logger.logError("UMP", "dbEndTransaction", error);
  }
};

/**
 * dbSaveWebData - Save Web Database
 */
UMP.prototype.dbSaveWebData = async function (success, fail) {
  try {
    if (FrameworkHelper.getPlatform() === "browser") {
      await DatabaseManager.getInstance().saveWebData();
    }
    success(true)
  } catch (error) {
    fail(error)
  }
};

/**
 * dbExportWebData - Export Web Database
 */
UMP.prototype.dbExportWebData = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};

/**
 * launchFile - Luanch file in deafult system application
 * @param filePath File complete path
 * @param callback (Optional) user supplied async callback / error handler
 */
UMP.prototype.launchFile = function (filePath, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};
/**
 * launchBase64 - Save Base64 string in a file and luanch the file in deafult system application
 * @param {string} base64 File content as base64 string
 * @param {string} fileName (Optional) file name to be saves as. Default name is "Temp"
 * @param {string} extension (Optional) file extension to be saves as. Default extension is ".pdf"
 * @param callback (Optional) user supplied async callback / error handler
 */
UMP.prototype.launchBase64 = function (base64String, fileName, extension, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};
/**
 * unzip - Unzip source file to destination path
 * @param srcPath Source file complete path
 * @param destPath Destination path
 * @param callback (Optional) user supplied async callback / error handler
 */
UMP.prototype.unzip = function (srcPath, destPath, success, fail) { 
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};

/**
 * getAttachmentFolderPath - Get attachment directory path
 * Required to get complete attachment file path in iOS. cancatenate this path with file name to get complete file path
 */
UMP.prototype.getAttachmentFolderPath = async function (success, fail) {
  try {
    const attachmentDir = await AttachmentHelper.getAttachmentDirectory();
    success(attachmentDir)
  } catch (error) {
    fail(error)
  }
};
/**
 * createAttachmentItem - Copy attachment file to application folder and insert attachment item to database with updated local path
 * @param {string} tableName attachment item table name
 * @param {Object} structureObject - Attachment Item Json object contains field name-value
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.createAttachmentItem = async function (tableName, structureObject, success, fail) {
  // Table name is required
  if (tableName == null || tableName == "") {
    fail("Table name is null or empty")
    return;
  }

  // Structure object is required
  if (structureObject == null || Object.keys(structureObject).length === 0) {
    fail("Structure object is null or empty")
    return;
  }

  if (structureObject[ServiceConstants.AttachmentItemFieldFileName] == null || structureObject[ServiceConstants.AttachmentItemFieldFileName] == "") {
    fail("File Name is null or empty")
    return;
  }

  // if (structureObject[ServiceConstants.AttachmentItemFieldMimeType] == null || structureObject[ServiceConstants.AttachmentItemFieldMimeType] == "") {
  //   fail("MIME Type is null or empty")
  //   return;
  // }

  if (structureObject[ServiceConstants.AttachmentItemFieldLocalPath] == null || structureObject[ServiceConstants.AttachmentItemFieldLocalPath] == "") {
    fail("Local Path is null or empty")
    return;
  }

  try {

    const arrayBuffer = await AttachmentHelper.readFileAsArrayBuffer(structureObject[ServiceConstants.AttachmentItemFieldLocalPath]);
    if (arrayBuffer == null) {
        fail(`Attachment file could not be read at the location: ${structureObject[ServiceConstants.AttachmentItemFieldLocalPath]}`)
        return;
    }

    let attachmentName = structureObject[ServiceConstants.AttachmentItemFieldUid];
    if (structureObject[ServiceConstants.AttachmentItemFieldFileName] != null && structureObject[ServiceConstants.AttachmentItemFieldFileName] != "") {
      attachmentName = structureObject[ServiceConstants.AttachmentItemFieldFileName];
    }
    const attachmentPath = await AttachmentHelper.addAttachment(attachmentName, arrayBuffer);
    structureObject[ServiceConstants.AttachmentItemFieldLocalPath] = attachmentPath;
    structureObject[ServiceConstants.AttachmentItemFieldAttachmentStatus] = ServiceConstants.AttachmentStatusSavedForUpload;
    await AppDatabaseManager.getInstance().insert(tableName, structureObject, false);
    success({data: structureObject, type: resultType.success})
  } catch (error) {
    console.log(JSON.stringify(error));
    fail({type: resultType.error, error: `There was an error saving the created attachment in DB: ${error}`})
  }
};
/**
 * uploadAttachment - Upload attachment
 * @param {string} tableName attachment item table name
 * @param {Object} structureObject - Attachment Item Json object contains field name-value
 * @param {boolean} isAsync - Upload attachment in Async or Sync. Default to Async
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.uploadAttachment = function (tableName, structureObject, isAsync, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    // NOT IMPLEMENTED IN NATIVE ALSO
    success(true)
  } catch (error) {
    fail(error)
  }
};
/**
 * downloadAttachment - Download attachment
 * @param {string} tableName attachment item table name
 * @param {Object} structureObject - Attachment Item Json object contains field name-value
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.downloadAttachment = async function (tableName, structureObject, success, fail) {
  try {
    await AttachmentQHelper.queueForDownload(tableName, structureObject, ServiceConstants.FwAttachmentForceDownloadPriority);
    success({data: structureObject, type: resultType.success})
    await AttachmentService().start();
  } catch (error) {
    fail({type: resultType.error, error: `There was an error downloading the attachment: ${error}`})
  }
};

/**
 * submitInSync - submit data to ump server in sync mode
 *
 * Example - ump.sync.submitInSync(CUSTOMER_INPUT_HEADER,"","UNVIRED_CUSTOMER_SEARCH_ECC_PA_GET_CUSTOMERS", true, callback);
 *
 * @param {requestType} reqype - Message request type(RQST/PULL/PUSH/QUERY/REQ) to be sent to the server.
 * @param {object} header - Header Datastructure  {"Header name": {field name : field value,...}}
 * @param {string} customData - custome data as string
 * @param {string} paFunction - Name of the process agent that is required to be called in the server.
 * @param {boolean} autoSave - flag to decide whether framework should save the data in databse or not.
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 */
UMP.prototype.syncForeground = async function (reqype, header, customData, paFunction, autoSave, success, fail) {
  try {
    const result = await SyncEngine.syncForeground(reqype, header, customData == null ? "" : customData, paFunction, autoSave);
    success(result)
  } catch (error) {
    fail(error)
  }
};
/*
  * submitDataInASync - submit data to ump server in async mode. Application will be notified through register NotificationListener callback.
  *
  * Example - ump.sync.submitInAsync(requestType.RQST,CUSTOMER_HEADER,"","UNVIRED_CUSTOMER_SEARCH_ECC_PA_GET_CUSTOMERS","CUSTOMER",CUSTOMER_HEADER.LID, true, callback);
  *
  * @param {requestType} reqType - Message request type (RQST/PULL/PUSH/QUERY) to be sent to the server.
  * @param {object} header - Header Datastructure object  {"Header name": {field name : field value,...}}
  * @param {string} customData -  custom data
  * @param {string} paFunction - Name of the process agent that is required to be called in the server.
  * @param {string} beName - Name of the BusinessEntity
  * @param {string} beLid - LID of Header
  * @param {boolean} bypassAttachment - boolean whether to ignore attachment while sending data to server
  * @param {function} callback - (Optional) user supplied async callback / error handler
  */
UMP.prototype.syncBackground = async function (reqype, header, customData, paFunction, beName, belid, bypassAttachment, success, fail) {
  try {
    const result = await SyncEngine.syncBackground(reqype, header, customData == null ? "" : customData, paFunction, beName);
    success(result)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.sendInitialDataDownloadRequest = async function (functions = []) {
  return await SettingsHelper.requestInitialDataDownload(functions)
}

/**
 * Returns an observable containing the state of the synchronisation along with count (if applicable). Possible values are as follows:
 * 1. Sending (count) // Ex: sending(3), there are 3 items in outbox and device is online. i.e datasender thread running
 * 2. Receiving // There are items to be received from server & device is online
 * 3. Processing (count) // Ex: processing (5), there are 5 items in inbox and they are being processed.
 * 4. Waiting to connect // The device is offline & there are items in outbox
 * 5. Idle // there is no synchronisation activity going on.
 */
UMP.prototype.getSynchronizationState = function (success, fail) {
  NotificationListenerHelper.synchronizationStateListener = success;
  NotificationListenerHelper.synchronizationStateListener("idle") 
};

/**
 * For Mobile platform only
 * Starts Inbox handler if there are items in inbox.
 */
UMP.prototype.startInboxHandler = function (success, fail) {
  InboxService.getInstance().start();
};

/**
 * For Mobile platform only
 * Starts Data sender if there are items in outbox
 */
UMP.prototype.startDataSender = function (success, fail) {
  OutboxService.getInstance().start();
};

/**
 * getMessages - Request for downloading messages in ready state from server and will be notified through Notification Listener
 *
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 *
 * Mobile Only api
 */
UMP.prototype.getMessages = function () {
  SyncEngine.getMessages();
};
/**
 * registerNotificationListener - Register for callback on GetMessage status
 *
 * @param {function} callback - (Optional) user supplied async callback / error handler
 *
 * Mobile Only api
 */
UMP.prototype.registerNotifListener = function (success, fail) {  
  NotificationListenerHelper.dataSenderListener = success;
};
/**
 * unRegisterNotificationListener - UnRegister for callback on GetMessage status
 *
 *  @param {function} callback - (Optional) user supplied async callback / error handler
 *
 * Mobile Only api
 */
UMP.prototype.unRegisterNotifListener = function () { 
  NotificationListenerHelper.dataSenderListener = null;
};
/**
 * isInOutbox - Check whether BE is already in OutBox or not.
 *
 * @param {string} beLid - LID of BE Header
 *
 * returns true/false
 */
UMP.prototype.isInOutBox = async function (beLid, success, fail) {
  try {
    const outObject = await OutBoxHelper.checkIsInOutBox(beLid);
    success(outObject !== null);
  } catch (error) {
    fail(error);
  }
};
/**
 * outBoxItemCount - Get count of items in OutBox
 *
 * returns count
 */
UMP.prototype.outBoxItemCount = async function (success, fail) {
  try {
    const count = await SettingsHelper.getOutboxCount();
    success(count);
  } catch (error) {
    fail(error);
  }
};
/**
 * isInSentItem - Check whether BE is already in SentItem or not.
 *
 * @param {string} beLid - LID of BE Header
 *
 * returns true/false
 */
UMP.prototype.isInSentItem = async function (beLid, success, fail) {
  try {
    const sentItem = await OutBoxHelper.checkIsInSentItems(beLid);
    success(sentItem !== null);
  } catch (error) {
    fail(error);
  }
};
/**
 * sentItemCount - Get count of items in Sentitem
 *
 * returns count
 */
UMP.prototype.sentItemCount = async function (success, fail) {
  try {
    const count = await SettingsHelper.getSentItemsCount();
    success(count);
  } catch (error) {
    fail(error);
  }
};
/**
 * inBoxItemCount - Get count of items in InBox
 *
 * returns count
 */
UMP.prototype.inBoxItemCount = async function (success, fail) {
  try {
    const count = await SettingsHelper.getInboxCount();
    success(count);
  } catch (error) {
    fail(error);
  }
};
/**
 * deleteOutBoxEntry - Delete BE from OutBox.
 *
 * @param {string} beLid - LID of BE Header
 *
 * returns true/false
 */
UMP.prototype.deleteOutBoxEntry = async function (beLid, success, fail) {
  try {
    const result = await OutBoxHelper.deleteOutBoxEntry(beLid);
    success(result);
  } catch (error) {
    fail(error);
  }
};

/**
* registerForPushNotification - Register for browser push
* Response types - open, error and message
*/
UMP.prototype.registerForPushNotification = async function (pushToken, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    await HttpConnection.registerNotification(pushToken);
    success(true)
  } catch (error) {
    fail(error)
  }
};

/**
 * resetApplicationSyncData - Reset application Sync related data(OutObject,SentItemObject,InObject,AttachmentOutObject,AttachmentQObject,Attachment folder).
 */
UMP.prototype.resetApplicationSyncData = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.generateUBJson = function (headerName, header, itemName, items, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.parseRawUBJson = function (json, sucess, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.lockDataSender = async function (beLid, success, fail) {
  try {
    const result = await SyncEngine.lockDataSender(beLid);
    success(result)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.unlockDataSender = async function (success, fail) {
  try {
    const result = await SyncEngine.unlockDataSender();
    success(result)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.dbReload = function (sucess, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};
UMP.prototype.reCreateAppDB = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.platform = function (success, fail) {
  try {
    const result = FrameworkHelper.getPlatform();
    success(result)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.removeOutObjectBasedOnLid = function (lid, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success(true)
  } catch (error) {
    fail(error)
  }
};

UMP.prototype.startDiscoveryService = function (timeout, success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};

/**
 * Refresh JWT Token. THe updated JWT Token would be sent through the NotificationListener methods.
 * @param {*} success Success Callback
 * @param {*} fail Failure Callback
 */
UMP.prototype.refreshJWTToken = function (success, fail) {
  try {
    // TODO: Implement this SettingsPlugin
    success("")
  } catch (error) {
    fail(error)
  }
};

/**
 * Set JWT Token. 
 * @param {string} token JWT Token
 * @param {*} success Success Callback
 * @param {*} fail Failure Callback
 */
UMP.prototype.setJWTToken = function (token, success, fail) {
  HttpConnection.jwtAuthToken = token;
  UserSettingsManager.getInstance().setFieldValue(UserSettingsFields.jwtToken, token);
  success(true)
}

 /**
   * Get UMP request configuration with authentication headers
   * @param {string} endpoint - The API endpoint
   * @param {Object} [options] - Optional configuration object
   * @param {string} [options.customUrl] - Optional custom base URL to override server URL
   * @param {function} success - Success callback
   * @param {function} fail - Failure callback
   * 
   * Example - 
   * 
   * // with auth api
   * ump.getUMPRequestConfig("/auth/getmodel/tenex/tenex_model.json", function (config) {
   *   console.log(config);  // https://sandbox.unvired.io/auth/getmodel/tenex/tenex_model.json
   * }, function (error) {
   *   console.error(error);
   * });
   * 
   * // with rest api end point
   * // variation: 1
   * ump.getUMPRequestConfig("/api/v1/users", function (config) {
   *   console.log(config);  // https://sandbox.unvired.io/UMP/api/v1/users
   * }, function (error) {
   *   console.error(error);
   * });
   * 
   * // variation: 2
   * ump.getUMPRequestConfig("/UMP/api/v1/users", function (config) {
   *   console.log(config);  // https://sandbox.unvired.io/UMP/api/v1/users
   * }, function (error) {
   *   console.error(error);
   * });
   */
 UMP.prototype.getUMPRequestConfig = function (endpoint, success, fail) {
  
  // Sanitize the input.
  if (!endpoint) {
    endpoint = '';
  }

  this.userSettings(function (settings) {
    let url = settings.data.SERVER_URL;
    let md5Password = settings.data.UNVIRED_PASSWORD;
    let jwtToken = settings.data.JWT_TOKEN;
    let unviredId = settings.data.UNVIRED_ID;

    // Remove trailing UMP or UMP/ from url if present
    url = url.replace(/\/UMP\//, '');
    url = url.replace(/\/UMP$/, '');

    // Remove leading slash from endpoint if present
    endpoint = endpoint.replace(/^\/+/, '');

    // Add a special case for api endpoints
    // always prefix with UMP/
    if (endpoint.startsWith("API/")) {
      endpoint = 'UMP/' + endpoint
    }

    let authHeader = "";
    if (unviredId && unviredId.length > 0 && md5Password && md5Password.length > 0) {
      authHeader = `Basic ${window.btoa(parameters.company + "\\" + unviredId + ":" + md5Password)}`;
    }
    else if (jwtToken && jwtToken.length > 0) {
      authHeader = `Bearer ${jwtToken}`;
    }

    if (authHeader.length == 0) {
      console.error("No valid authentication headers found");
      fail("No valid authentication headers found: userId: " + unviredId + ", md5Password: " + md5Password + ", jwtToken: " + jwtToken);
      return;
    }

    let requestConfig = {
        url: `${url}/${endpoint}`,
        headers: {
            Authorization: authHeader
        }
    };

    success(requestConfig);

  }, function (error) {
    fail(error);
  });
}

helper.isEmpty = function (value) {
  if (value == undefined || value === null || value === "") return true;
  return false;
};
helper.validateLoginParameters = function (mode, callback) {
  if (this.isEmpty(parameters.loginType)) {
    this.sendError("No Login Type specified in LoginParameters!", callback);
    return false;
  }

  if (parameters.loginType === loginType.sap || parameters.loginType === loginType.ads) {
    if (!parameters.domain) {
      this.sendError("Please provide Domain!", callback);
      return false;
    }
  }
  if (this.isEmpty(mode)) {
    this.sendError("Please set Login Mode!", callback);
    return false;
  }
  var err = undefined;
  switch (mode) {
    case loginMode.authActivate:

      if (parameters.loginType == loginType.saml2) {
        if (this.isEmpty(parameters.url)) err = "Please provide Url!";
        else if (this.isEmpty(parameters.company)) err = "Please provide Company Name!";
        else if (this.isEmpty(parameters.redirectURL)) err = "Please provide SAML redirect URL!";
        break;
      }
      else {
        if (this.isEmpty(parameters.url)) err = "Please provide Url!";
        else if (this.isEmpty(parameters.company)) err = "Please provide Company Name!";
        else if (this.isEmpty(parameters.username)) err = "Please provide User Id!";
        else if (this.isEmpty(parameters.password)) err = "Please provide Password!";
      }
      if (err) {
        this.sendError(err, callback);
        return false;
      }
      break;
    case loginMode.authLocal:
      if (this.isEmpty(parameters.username)) err = "Please provide User Id!";
      else if (this.isEmpty(parameters.password)) err = "Please provide Password!";
      if (err) {
        this.sendError(err, callback);
        return false;
      }
      break;
    case loginMode.forgotPassword:
      break;
  }
  return true;
};
helper.guid = function () {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1)
      .toUpperCase();
  }
  return s4() + s4() + "-" + s4() + "-" + s4() + "-" + s4() + "-" + s4() + s4() + s4();
};

/**
 * Clear loki databases
 */
helper.clearLokiDbs = function () {
  localStorage.removeItem("APP_LOKI_DB");
  localStorage.removeItem("FW_LOKI_DB");
  localStorage.removeItem(login.parameters.appName);
  webDb.appDb = null;
  webDb.fwDb = null;
};

helper.sendError = function (msg, callback) {
  var cbResult = {};
  cbResult.type = resultType.error;
  cbResult.error = msg;
  callback(cbResult);
};

helper.sanitizeUMPURL = function (url) {
  if (!url) {
    return "";
  }
  if (url.endsWith("/UMP") || url.endsWith("/UMP/") || url.endsWith("?local")) {
    return url;
  }
  return url + "/UMP";
};

function buildWhereClause(conditions) {
  if (!conditions || Object.keys(conditions).length === 0) {
      return '';
  }

  return Object.entries(conditions)
      .map(([key, value]) => {
          // Handle null values
          if (value === null) {
              return `${key} IS NULL`;
          }
          // Handle string values (add quotes)
          if (typeof value === 'string') {
              return `${key} = '${value}'`;
          }
          // Handle numbers and other types
          return `${key} = ${value}`;
      })
      .join(' AND ');
}

module.exports = new UMP();