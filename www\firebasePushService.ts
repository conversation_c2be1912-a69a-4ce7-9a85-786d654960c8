
// Firebase Push Notification Service for iOS and Android
// Requires cordova-plugin-firebasex
import { XMLParser } from 'fast-xml-parser';
import { NotificationConstants } from './helper/notificationConstants';
import { UnviredAccountManager } from './helper/unviredAccountManager';
import SyncEngine from './syncEngine';

declare var Logger: any;

// Placeholder Notification class for compatibility. Replace with your actual implementation if available.

class Notification {
    notificationType: string = "";
    alert: string = "";
    title: string = "";
    actionButton: string = "";
    silent: boolean = false;
    badge: number = 0;
    category: string = "";
    sound: string = "default";
    attachmentUID: string = "";
    notificationContext: string = "";
    activationId: string = "";

    constructor(init?: Partial<Notification>) {
        Object.assign(this, init);
    }
}

export class FirebasePushService {
    static registerForPushNotifications(onRegister: (token: string) => void, onError?: (error: any) => void) {
        if (window?.cordova?.plugins?.firebase) {
            // Request permission for push notifications
            window.cordova.plugins.firebase.messaging.requestPermission({ forceShow: false }).then(function () {
                console.log("Push messaging is allowed");
                FirebasePushService.registerTokenHandlers(onRegister, onError);
            }).catch(function (error: any) {
                Logger.logError("FirebasePushService", "registerForPushNotifications", "Error requesting push notification permission: " + error);
                if (onError) onError('FirebaseMessagingPlugin not available');
            });
        } else {
            console.warn("Warning: FirebaseMessagingPlugin not available");
            if (onError) onError('FirebaseMessagingPlugin not available');
        }
    }

    private static registerTokenHandlers(onRegister: (token: string) => void, onError?: (error: any) => void) {
        if (window?.cordova?.plugins?.firebase) {
            window.cordova.plugins.firebase.messaging.getToken().then(function(token: any) {
                Logger.logDebug("FirebasePushService", "registerTokenHandlers", "Got device token: " + token);
                onRegister(token);
            });

            window.cordova.plugins.firebase.messaging.onTokenRefresh(function(token: any) {
                Logger.logDebug("FirebasePushService", "registerTokenHandlers", "Got new device token: " + token);
                onRegister(token);
            });
        }
        else {
            console.warn("Warning: FirebaseMessagingPlugin not available");
        }
    }

    static onNotificationReceived(onMessage: (data: any) => void, onError?: (error: any) => void) {
        if (window?.cordova?.plugins?.firebase) {
            window.cordova.plugins.firebase.messaging.onBackgroundMessage(function(payload: any) {
                console.log("New background FCM message: ", JSON.stringify(payload, null, 2));
                onMessage(FirebasePushService.parseNotification(payload));
            });

            window.cordova.plugins.firebase.messaging.onMessage(function(payload: any) {
                console.log("New foreground FCM message: ", JSON.stringify(payload, null, 2));
                onMessage(FirebasePushService.parseNotification(payload));
            });
        } else {
            console.warn("Warning: FirebaseMessagingPlugin not available");
            if (onError) onError('FirebaseMessagingPlugin not available');
        }
    }

    static parseNotification(data: any): any {
        let parsedData = data.data || {};
        // If data.data is XML, convert it to JSON using fast-xml-parser
        if (typeof parsedData === 'string' && parsedData.trim().length > 0 && parsedData.trim().startsWith('<') && parsedData.trim().endsWith('>')) {
            try {
                const parser = new XMLParser({
                    ignoreAttributes: false,
                    attributeNamePrefix: '_',
                    textNodeName: 'content',
                    ignoreDeclaration: true,
                    parseAttributeValue: false,
                    trimValues: true,
                    isArray: () => false
                });
                parsedData = parser.parse(parsedData);
            } catch (e) {
                // If conversion fails, keep as string
                return new Notification();
            }
        } else if (typeof parsedData === 'string') {
            return new Notification();
        }

        // TypeScript equivalent of Dart notification parsing logic
        let jsonData: any = {};
        const notificationDataJson = parsedData;
        if (notificationDataJson && typeof notificationDataJson === 'object') {
            if (notificationDataJson.hasOwnProperty(NotificationConstants.UNVIRED_PUSH_NOTIFICATION)) {
                jsonData = notificationDataJson[NotificationConstants.UNVIRED_PUSH_NOTIFICATION];
            } else if (notificationDataJson.hasOwnProperty(NotificationConstants.INDIENCE_PUSH_NOTIFICATION)) {
                jsonData = notificationDataJson[NotificationConstants.INDIENCE_PUSH_NOTIFICATION];
            } else if (notificationDataJson.hasOwnProperty(NotificationConstants.INDIENCE_PUSH_NOTIFICATION_TEMP)) {
                jsonData = notificationDataJson[NotificationConstants.INDIENCE_PUSH_NOTIFICATION_TEMP];
            }
        }

        const notification = new Notification();
        if (jsonData && Object.keys(jsonData).length > 0) {
            notification.notificationType = jsonData[NotificationConstants.UNDERSCORE_TYPE] || "";
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_MESSAGE)) {
                notification.alert = jsonData[NotificationConstants.PUSH_NOTIFICATION_MESSAGE] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_TITLE)) {
                notification.title = jsonData[NotificationConstants.PUSH_NOTIFICATION_TITLE] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_ACTION_BUTTON)) {
                notification.actionButton = jsonData[NotificationConstants.PUSH_NOTIFICATION_ACTION_BUTTON] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_SILENT)) {
                notification.silent = jsonData[NotificationConstants.PUSH_NOTIFICATION_SILENT].toString() === 'true';
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_COUNT)) {
                notification.badge = parseInt(jsonData[NotificationConstants.PUSH_NOTIFICATION_COUNT].toString(), 10);
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_CATEGORY)) {
                notification.category = jsonData[NotificationConstants.PUSH_NOTIFICATION_CATEGORY] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_SOUND)) {
                notification.sound = jsonData[NotificationConstants.PUSH_NOTIFICATION_SOUND] || "default";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_ATTACHMENT_ID)) {
                notification.attachmentUID = jsonData[NotificationConstants.PUSH_NOTIFICATION_ATTACHMENT_ID] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_CONTEXT)) {
                notification.notificationContext = jsonData[NotificationConstants.PUSH_NOTIFICATION_CONTEXT] || "";
            }
            if (jsonData.hasOwnProperty(NotificationConstants.PUSH_NOTIFICATION_ACTIVATION_ID)) {
                notification.activationId = jsonData[NotificationConstants.PUSH_NOTIFICATION_ACTIVATION_ID] || "";
            }
        }
        
        if (notification.notificationType == NotificationConstants.SYSTEM || notification.notificationType == NotificationConstants.USER) {
            const account = UnviredAccountManager.getInstance().getLastLoggedInAccount();
            if (account != null && account != undefined) {
                Logger.logInfo("FirebasePushService", "parseNotification",
                    "Starting Download message.");
                SyncEngine.getMessages()
            }
        } else if (notification.notificationType == NotificationConstants.ATTACHMENT) {}

        return notification;
    }
}

export default FirebasePushService;
