import AttachmentQHelper from "../attachment/attachmentQHelper";
import { AuthenticationService } from "../authenticationService";
import DatabaseManager, { DatabaseType } from "../database/databaseManager";
import InboxHelper from "../inbox/inboxHelper";
import OutBoxHelper from "../outbox/outboxHelper";
import OutboxService from "../outbox/outboxService";
import { SyncEngine } from "../syncEngine";
import FrameworkHelper from "./frameworkHelper";
import { FrameworkSettingsFields, FrameworkSettingsManager } from "./frameworkSettingsManager";
import { HttpConnection } from "./httpConnection";
import * as ServiceConstants from "./serviceConstants";
import { UserSettingsFields, UserSettingsManager } from "./userSettingsManager";
import { ObjectStatus, SyncStatus } from "./utils";

export enum LogLevel {
  debug = "DEBUG",
  error = "ERROR",
  important = "IMPORTANT"
}

declare var Logger: any;

class SettingsHelper {
  static async getFrameworkVersionNumber(): Promise<string> {
    return ServiceConstants.FrameworkVersionNumber;
  }

  static async getFrameworkBuildNumber(): Promise<string> {
    return ServiceConstants.FrameworkBuildNumber;
  }

  static async getApplicationName(): Promise<string> {
    return AuthenticationService.instance.loginParameters.appName;
  }

  static async getApplicationVersionNumber(): Promise<string> {
    return AuthenticationService.instance.loginParameters.appVersion;
  }

  static async getFrameworkRevisionNumber(): Promise<string> {
    return ServiceConstants.FrameworkRevisionNumber;
  }

  static async getApplicationRevisionNumber(): Promise<string> {
    return AuthenticationService.instance.loginParameters.appVersion;
  }

  static async getApplicationDBVersion(): Promise<string> {
    const databaseManager = DatabaseManager.getInstance();
    const allAppMetas = await databaseManager.select(DatabaseType.FrameworkDb, "ApplicationMeta");
    if (allAppMetas.length === 0) {
      return "";
    }
    return allAppMetas[0].version;
  }

  static async getApplicationBuildNumber(): Promise<string> {
    return ""
  }

  static async getInboxCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "InObject");
  }

  static async getOutboxCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "OutObject");
  }

  static async getSentItemsCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "SentItems");
  }

  static async getAttachmentCount(): Promise<number> {
    return await DatabaseManager.getInstance().count(DatabaseType.FrameworkDb, "AttachmentQObject");
  }

  static async isInOutBoxQueue(beHeaderLid: string): Promise<boolean> {
    const outObject = await OutBoxHelper.checkIsInOutBox(beHeaderLid);
    return outObject !== null;
  }

  static async isInSentItems(beHeaderLid: string): Promise<boolean> {
    const sentItem = await OutBoxHelper.checkIsInSentItems(beHeaderLid);
    return sentItem !== null;
  }

  static async sendLogsToServer(): Promise<void> {
    try {
      // Get the zipped log file URL
      const zipUrl = await SettingsHelper.createAndGetLogZipPath();
      if (!zipUrl) {
        await Logger.logError('SettingsHelper', 'sendLogsToServer', 'No zipped log file found to upload.');
        return;
      }

      const response = await new HttpConnection().uploadLogOrData(zipUrl, ServiceConstants.ActionUploadLogs);
      if (response && response.ok) {
        await Logger.logInfo('SettingsHelper', 'sendLogsToServer', 'Logs uploaded successfully.');
      } else {
        const errorText = response ? await response.text() : 'No response';
        await Logger.logError('SettingsHelper', 'sendLogsToServer', `Upload failed: ${errorText}`);
      }

      // Clean up the temporary URL
      URL.revokeObjectURL(zipUrl);
    } catch (e) {
      await Logger.logError('SettingsHelper', 'sendLogsToServer', `Error uploading logs: ${e}`);
    }
  }

  static async createAndGetLogZipPath(): Promise<string> {
    try {
      // 1. Read the actual log file content
      let logText = await SettingsHelper.getLogFileContent();
      if (typeof logText !== 'string') logText = '';
      // Append framework version
      let fullLogText = logText;
      // 2. Append Additional Info like Inbox / Outbox count to the log file
      const additionalMessages = await (new SettingsHelper()).getAdditionalInfo();
      fullLogText += additionalMessages;
      // Prepare the main log file as a Blob
      const logBlob = new Blob([fullLogText], { type: 'text/plain' });
      // 3. Handle backup log file if exists
      let backupBlob: Blob | null = null;
      let backupFileName = '';
      try {
        let backupText = await SettingsHelper.getBackupLogFileContent();
        if (typeof backupText !== 'string') backupText = '';
        if (backupText.length > 0) {
          backupBlob = new Blob([backupText], { type: 'text/plain' });
          backupFileName = 'backup_log.txt';
        }
      } catch (e) {
        // No backup log, skip
      }

      // 4. Zip the log file(s)
      // @ts-ignore
      const { zipSync, strToU8 } = typeof require !== 'undefined' ? require('fflate') : window.fflate;
      const files: { [name: string]: Uint8Array } = {};
      // Read logBlob as Uint8Array
      const logArr = new Uint8Array(await logBlob.arrayBuffer());
      files['log.txt'] = logArr;
      if (backupBlob) {
        const backupArr = new Uint8Array(await backupBlob.arrayBuffer());
        files[backupFileName] = backupArr;
      }
      const zipped = zipSync(files);

      // 5. Create a Blob and a temporary URL for the zip
      const zipBlob = new Blob([zipped], { type: 'application/zip' });
      const zipUrl = URL.createObjectURL(zipBlob);

      // Schedule cleanup of the object URL after 3 minutes
      setTimeout(() => {
        URL.revokeObjectURL(zipUrl);
        Logger && Logger.logDebug && Logger.logDebug('SettingsHelper', 'createAndGetLogZipPath', 'Revoked object URL for zipped logs after 3 minutes.');
      }, 3 * 60 * 1000);

      // Return the URL (or you could return the Blob if needed)
      return zipUrl;
    } catch (e) {
      await Logger.logError('SettingsHelper', 'createAndGetLogZipPath', `Error creating log zip: ${e}`);
      return "";
    }
  }

  static async sendAppDbToServer(): Promise<void> {
    try {
      // 1. Get the DB file path from Cordova plugin
      const dbPath = await SettingsHelper.getAppDbPath()
      if (!dbPath) {
        await Logger.logError('SettingsHelper', 'sendAppDbToServer', 'No DB path found.');
        return;
      }

      // 2. Read the DB file as Blob
      // Always use file:// URL for Cordova File API
      let fileUrl = dbPath;
      if (!dbPath.startsWith('file://')) {
        fileUrl = 'file://' + dbPath;
      }
      const dbBlob: ArrayBuffer = await new Promise((resolve, reject) => {
        window.resolveLocalFileSystemURL(fileUrl, (fileEntry: any) => {
          fileEntry.file((file: any) => {
            const reader = new FileReader();
            reader.onloadend = () => {
              Logger.logInfo("SettingsHelper", "sendAppDbToServer", "Read completed");
              resolve(reader.result as ArrayBuffer);
            };
            reader.onerror = (e) => {
              Logger.logError("SettingsHelper", "sendAppDbToServer", "Read error: " + e);
              reject(e);
            };
            reader.readAsArrayBuffer(file);
          }, (err: any) => {
            reject(err);
          });
        }, (err: any) => {
          reject(err);
        });
      });

      // 3. Zip the DB file
      // @ts-ignore
      const { zipSync } = typeof require !== 'undefined' ? require('fflate') : window.fflate;
      const dbArr = new Uint8Array(dbBlob);
      const files: { [name: string]: Uint8Array } = { 'app.db': dbArr };
      const zipped = zipSync(files);

      // 4. Create a Blob and object URL for the zip
      const zipBlob = new Blob([zipped], { type: 'application/zip' });
      const zipUrl = URL.createObjectURL(zipBlob);

      // 5. Upload to server
      const response = await new HttpConnection().uploadLogOrData(zipUrl, ServiceConstants.ActionUploadData);
      if (response && response.ok) {
        await Logger.logInfo('SettingsHelper', 'sendAppDbToServer', 'App DB uploaded successfully.');
      } else {
        const errorText = response ? await response.text() : 'No response';
        await Logger.logError('SettingsHelper', 'sendAppDbToServer', `Upload failed: ${errorText}`);
      }

      // 6. Clean up
      URL.revokeObjectURL(zipUrl);
    } catch (e) {
      await Logger.logError('SettingsHelper', 'sendAppDbToServer', `Error uploading app DB: ${e}`);
    }
  }

  static async deleteLogs(): Promise<void> {
    // TODO: Implement this method
  }
  
  static async requestInitialDataDownload(functions: Array<{ [key: string]: any }> = []): Promise<void> {
    const serverId = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.serverId);
    const applicationId = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.applicationId);
    const namespace = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.namespace);

    const dataObject = {
      "companyAlias": AuthenticationService.instance.loginParameters.company,
      "serverId": serverId,
      "applicationId": applicationId,
      "namespace": namespace,
      "applicationName": AuthenticationService.instance.loginParameters.appName,
      "type": 9000,
      "subtype": 400,
      "functions": functions
    };

    const inputJson: any = {};
    inputJson[ServiceConstants.QueryParamInputMessage] = JSON.stringify(dataObject)

    const dataString = Object.keys(dataObject).length === 0 ? "" : JSON.stringify(inputJson);

    const outObjectData = {
      lid: FrameworkHelper.getUUID(),
      timestamp: Date.now(),
      objectStatus: ObjectStatus.global,
      syncStatus: SyncStatus.none,
      functionName: ServiceConstants.AdminServiceInitialDownload,
      beName: "",
      beHeaderLid: "",
      requestType: "",
      syncType: ServiceConstants.SyncType.SYNC.toString(),
      conversationId: "",
      messageJson: dataString,
      companyNameSpace: "",
      sendStatus: "",
      fieldOutObjectStatus: ServiceConstants.OutObjectStatus.none.toString(),
      isAdminServices: true
    };

    try {
      const databaseManager = DatabaseManager.getInstance();
      await databaseManager.insert(DatabaseType.FrameworkDb, "OutObject", outObjectData, true);
      await Logger.logInfo("SettingsHelper", "requestInitialDataDownload", "Starting Outbox Service");
      OutboxService.getInstance().start();
    } catch (e) {
      throw e;
    }
  }

  static async testPushNotification(): Promise<void> {
    // TODO: Implement this method
  }

  static async getCompleteLogs(): Promise<string> {
    // TODO: Implement this method
    return "";
  }

  static async getInfoMessages(): Promise<any[]> {
    try {
        const databaseManager = DatabaseManager.getInstance();
        const infoMessages = await databaseManager.select(DatabaseType.FrameworkDb, "InfoMessage");
        Logger.logDebug("SettingsHelper", "getApplicationVersion", "Info Messages: " + infoMessages);
        return infoMessages;
    } catch (e) {
        Logger.logError("SettingsHelper", "getApplicationVersion", e);
        return [];
    }
  }

  static async setRequestTimeout(timeout: number): Promise<void> {
    try {
        Logger.logDebug("SettingsHelper", "setRequestTimeout", "Setting request timeout to " + timeout);
        await UserSettingsManager.getInstance().setFieldValue(UserSettingsFields.requestTimeout, timeout);
    } catch (e) {
        Logger.logError("SettingsHelper", "setRequestTimeout", e);
    }
  }

  static async getRequestTimeout(): Promise<number> {
    try {
        const requestTimeout = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.requestTimeout);
        return parseInt(requestTimeout);
    } catch (e) {
        Logger.logError("SettingsHelper", "getRequestTimeout", e);
        return 0;
    }
  }

  static setLogLevel(logLevel: string): void {
    Logger.logDebug(
        "SettingsHelper", "setLogLevel", `logLevel: ${logLevel}`);
    let logNumber = "";
    switch (logLevel) {
        case LogLevel.important:
        case "7":
            logNumber = "7";
            Logger.setLogLevel(LogLevel.important);
            break;
        case LogLevel.error:
        case "8":
            logNumber = "8";
            Logger.setLogLevel(LogLevel.error);
            break;
        case LogLevel.debug:
        case "9":
            logNumber = "9";
            Logger.setLogLevel(LogLevel.debug);
            break;
        default:
            // Handle default case if needed
            break;
    }
    (async () => {
      await FrameworkSettingsManager.getInstance()
        .setFieldValue(FrameworkSettingsFields.logLevel, logNumber);
    })
  }

  static async getLogLevel(): Promise<string> {
    try {
        const logLevel = await FrameworkSettingsManager.getInstance().getFieldValue(FrameworkSettingsFields.logLevel);
        switch (logLevel) {
            case "7":
                return ServiceConstants.logImportant;
            case "8":
                return ServiceConstants.logError;
            case "9":
                return ServiceConstants.logDebug;
            default:
                return "";
        }    
    } catch (e) {
        Logger.logError("SettingsHelper", "getLogLevel", e);
        return "";
    }
  }

  static async getFetchInterval(): Promise<number> {
    try {
        const fetchInterval = await UserSettingsManager.getInstance().getFieldValue(UserSettingsFields.fetchInterval);
        return fetchInterval ? parseInt(fetchInterval) : 0;
    } catch (e) {
        Logger.logError("SettingsHelper", "getFetchInterval", e);
        return 0;
    }
  }

  static async clearData() {
    await AuthenticationService.instance.clearData();
  }
  
  static async queuePingToOutbox(): Promise<void> {
    let outObjectData = {
      lid: FrameworkHelper.getUUID(),
      timestamp: Date.now(),
      objectStatus: ObjectStatus.global,
      syncStatus: SyncStatus.none,
      functionName: "",
      beName: "",
      beHeaderLid: "",
      requestType: "",
      syncType: ServiceConstants.SyncType.ASYNC.toString(),
      conversationId: "",
      messageJson: ServiceConstants.AdminServicePing,
      companyNameSpace: "",
      sendStatus: "",
      fieldOutObjectStatus: ServiceConstants.OutObjectStatus.none.toString(),
      isAdminServices: true
    }
    await new SyncEngine().checkInOutBoxAndQueue(outObjectData);
    return;
  }

  private async getAdditionalInfo(): Promise<string> {
    let infoMessageString = "";

    const databaseManager = DatabaseManager.getInstance();
    const infoMessages = await databaseManager.select(DatabaseType.FrameworkDb, "InfoMessage");
    infoMessageString += "Framework Version: BUILD 6\n";
    if (infoMessages.length > 0) {
      // Add the Info Message to the end of Data.

      for (let i = 0; i < infoMessages.length; i++) {
        if (i === 0) {
          infoMessageString += "********ALL INFO MESSAGES********";
        }

        const infoMessage = infoMessages[i];
        const category = infoMessage.category;
        const message = infoMessage.message;
        const beName = infoMessage.bename;
        const beLID = infoMessage.belid;
        infoMessageString += `\n${i + 1}`;
        infoMessageString += `\n${message}`;
        infoMessageString += `\n${category}`;
        infoMessageString += `\n${beName}`;
        infoMessageString += `\n${beLID}`;
        infoMessageString += "\n";
        if (i === infoMessages.length - 1) {
          infoMessageString += "********END OF INFO MESSAGES********\n";
        }
      }

      // Display All the Contents from ApplicationVersion.txt.
      infoMessageString += "********VERSION INFORMATION********\n";
      
      // TODO: Get the version and build number from the package info.
      const version = "";
      const buildNumber = "";

      infoMessageString += `Version: ${version}`;
      infoMessageString += `\tBuild: ${buildNumber}`;
    }

    infoMessageString += "\n";

    // Add inbox items, outbox items and sent items details
    const inboxItems = await databaseManager.select(DatabaseType.FrameworkDb, "InObject");
    if (inboxItems.length > 0) {
      let inboxStr = "***** INBOX *****\n";
      for (const inObj of inboxItems) {
        const currentDate = new Date(inObj.timestamp);
        const formatter = new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const finalDate = formatter.format(currentDate);
        inboxStr += `${finalDate} | ${inObj.conversationId}\n`;
      }
      inboxStr += "\n";
      infoMessageString += inboxStr;
    }

    const outboxItems = await databaseManager.select(DatabaseType.FrameworkDb, "OutObject");
    if (outboxItems.length > 0) {
      let outboxStr = "***** OUTBOX *****\n";
      for (const outObj of outboxItems) {
        const currentDate = new Date(outObj.timestamp);
        const formatter = new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const finalDate = formatter.format(currentDate);
        outboxStr += `${finalDate} | ${outObj.conversationId}\n`;
      }
      outboxStr += "\n";
      infoMessageString += outboxStr;
    }

    const sentItems = await databaseManager.select(DatabaseType.FrameworkDb, "SentItems");
    if (sentItems.length > 0) {
      let sentItemsStr = "***** SENT ITEMS *****\n";
      for (const sentItem of sentItems) {
        const currentDate = new Date(sentItem.timestamp);
        const formatter = new Intl.DateTimeFormat('en-US', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        const finalDate = formatter.format(currentDate);
        sentItemsStr += `${finalDate} | ${sentItem.conversationId}\n`;
      }
      sentItemsStr += "\n";
      infoMessageString += sentItemsStr;
    }

    return infoMessageString;
  }

  private static async getLogFileContent(): Promise<string> {
    return new Promise(async (resolve, reject) => {
      Logger.getLogFileContent((logText: string) => {
        if (typeof logText !== 'string') logText = '';
        resolve(logText);
      }, (error: any) => {
        console.error("Error getting log file content:", error);
        resolve("");
      });
    });
  }

   private static async getBackupLogFileContent(): Promise<string> {
    return new Promise(async (resolve, reject) => {
      Logger.getBackupLogFileContent((logText: string) => {
        if (typeof logText !== 'string') logText = '';
        resolve(logText);
      }, (error: any) => {
        console.error("Error getting log file content:", error);
        resolve("");
      });
    });
  }

  private static async getAppDbPath(): Promise<string> {
    return new Promise((resolve, reject) => {
      window.UnviredDB.getDBFilePath({ dbType: "AppDb" }, (path) => {
        resolve(path)
      }, (error) => {
        Logger.logError("SettingsHelper", "getAppDbPath", "Error getting App DB path: " + error);
        resolve("")
      });
    })
  }

  convertMapToBase64(map: { [key: string]: any }): string {
    return btoa(JSON.stringify(map));
  }

  convertBase64ToMap(base64String: string): { [key: string]: any } {
    return JSON.parse(atob(base64String));
  }
}

export default SettingsHelper;